defmodule Drops.Relations.Plugins.QueryTest do
  use Drops.RelationCase, async: false

  describe "query composition" do
    relation(:users) do
      schema("users", infer: true)
    end

    import Drops.Relation.Plugins.Query, only: [query: 3]

    test "query functions work with actual data", %{users: users} do
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: true})
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: true})

      result =
        users
        # the macro should rewrite this internally to Drop.Relation.Operations.Or.new(users.get_by_name("<PERSON>"), users.get_by_name("<PERSON>"))
        # and this struct should be a queryable that can compose Ecto queries
        |> query([:u], :u.get_by_name("<PERSON>") or :u.get_by_name("<PERSON>"))
        |> Enum.to_list()

      # Sort by name to ensure consistent ordering since UNION doesn't guarantee order
      sorted_result = Enum.sort_by(result, & &1.name)
      assert [%{name: "<PERSON>"}, %{name: "<PERSON>"}] = sorted_result
    end
  end
end
