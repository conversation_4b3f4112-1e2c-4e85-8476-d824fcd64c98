defmodule Drops.Relation.Plugins.Query do
  use Drops.Relation.Plugin

  defmacro query(source, bindings, expression) do
    # Parse the bindings to get the atom name
    [binding_atom] = bindings

    # Transform the expression directly at compile time
    # We need to handle the binding atom replacement here
    transformed_expr = transform_expression(expression, binding_atom, source)

    quote do
      unquote(transformed_expr)
    end
  end

  def on(:before_compiler, _relation, _attributes) do
  end

  # Transform expressions by replacing binding atoms with the actual source
  defp transform_expression({:or, _meta, [left, right]}, binding_atom, source) do
    # Transform both sides of the OR
    left_transformed = transform_expression(left, binding_atom, source)
    right_transformed = transform_expression(right, binding_atom, source)

    # Create an OR operation
    quote do
      Drops.Relation.Operations.Or.new(
        unquote(left_transformed),
        unquote(right_transformed),
        unquote(source)
      )
    end
  end

  defp transform_expression(
         {{:., dot_meta, [atom, function_name]}, call_meta, args},
         binding_atom,
         source
       )
       when is_atom(function_name) do
    # Check if this is a call on our binding atom
    case atom do
      ^binding_atom ->
        # Replace the binding atom with the source
        quote do
          unquote(source).unquote(function_name)(unquote_splicing(args))
        end

      _ ->
        # Not our binding atom, leave as is
        {{:., dot_meta, [atom, function_name]}, call_meta, args}
    end
  end

  defp transform_expression(expr, _binding_atom, _source) do
    # For any other expression, return as is
    expr
  end
end
